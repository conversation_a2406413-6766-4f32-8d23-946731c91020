import {Component, effect, inject, model, OnInit} from '@angular/core';
import {CenterPanelToolComponent} from '../../../../../ui/layout/center-panel-tool/center-panel-tool.component';
import {CopyInputComponent} from '../../../../../ui/components/copy-input/copy-input.component';
import {InlineErrorBlockComponent} from '../../../../../ui/components/inline-error-block/inline-error-block.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {ToolDescription} from '@common/types/tool-description';
import * as toolInfo from '@common/data/tools/N/numeronym-generator.json';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-numeronym',
    imports: [
        CenterPanelToolComponent,
        CopyInputComponent,
        InlineErrorBlockComponent,
        ReactiveFormsModule,
        FormsModule,
    ],
    templateUrl: './numeronym.component.html',
    styleUrl: './numeronym.component.scss'
})
export class NumeronymComponent implements OnInit {
  stats = '';
  input = model('Internationalization');
  error = '';
  output = '';

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    effect(() => {
      this.input();
      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    this.error = '';
    const text = this.input().trim();

    this.output = '';

    const len = text.length;

    if (len > 2) {
      this.output = `${text[0]}${len - 2}${text[len - 1]}`;
    } else {
      this.output = text;
    }

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: text.length,
      outputLength: this.output.length
    });
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }
}

