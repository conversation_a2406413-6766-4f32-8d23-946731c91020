import {Component, inject, OnInit, signal} from '@angular/core';
import {IntegerInputComponent} from '@/app/ui/components/integer-input/integer-input.component';
import {PlayComponent} from '@/app/ui/icons/play/play.component';
import {TextOutputComponent} from '@/app/ui/components/text-output/text-output.component';
import {ToolDescription} from '@common/types/tool-description';

import * as toolInfo from '@common/data/tools/N/nano-id-generator.json';
import {nanoid} from 'nanoid';
import {FormsModule} from '@angular/forms';
import {ClickRestoreDirective} from '@/app/ui/components/click-restore.directive';
import {WheelNumberDirective} from '@/app/ui/components/wheel-number.directive';
import { CenterPanelToolComponent } from '@/app/ui/layout/center-panel-tool/center-panel-tool.component';
import { MarkdownComponent } from 'ngx-markdown';
import manual from '@common/data/tools/N/nano-id-generator.md';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-nano-id-generator',
  imports: [
    FormsModule,
    IntegerInputComponent,
    PlayComponent,
    TextOutputComponent,
    ClickRestoreDirective,
    WheelNumberDirective,
    CenterPanelToolComponent,
    MarkdownComponent,
  ],
    templateUrl: './nano-id-generator.component.html',
    styleUrl: './nano-id-generator.component.scss'
})
export class NanoIdGeneratorComponent implements OnInit {
  output = signal('');
  stats = signal('');
  defaultQuantity = 10;
  quantity = this.defaultQuantity;
  minQuantity = 1;
  maxQuantity = 1000000;
  defaultLength = 21;
  length = this.defaultLength;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    // console.log(`Generating ${this.quantity} Nano IDs with length ${this.length}`);
    const count = Number(this.quantity);

    const result: string[] = [];

    const start = performance.now();
    for(let i = 0; i < count; i++) {
      result.push(nanoid(this.length));
    }
    const end = performance.now();
    const time = (end - start) / 1000;

    this.stats.set(`Done in ${time.toFixed(2)}s`);
    this.output.set(result.join('\n'));

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      quantity: count,
      length: this.length
    });
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }

  protected readonly manual = manual;
}
