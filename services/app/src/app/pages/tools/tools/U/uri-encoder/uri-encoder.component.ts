import {Component, effect, inject, OnInit, signal} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {TextInputComponent} from '../../../../../ui/components/text-input/text-input.component';
import {TextOutputComponent} from '../../../../../ui/components/text-output/text-output.component';
import {ToolDescription} from '@common/types/tool-description';
import {debounceTime, Subject} from 'rxjs';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
import * as toolInfo from '@common/data/tools/U/uri-encoder.json';
import { ExclamationTriangleComponent } from '../../../../../ui/icons/exclamation-triangle/exclamation-triangle.component';
import { TwoPanelToolComponent } from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import {
  PanelTitleWithErrorComponent
} from '../../../../../ui/components/panel-title-with-error/panel-title-with-error.component';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-uri',
    imports: [
        FormsModule,
        TextInputComponent,
        TextOutputComponent,
        ExclamationTriangleComponent,
        TwoPanelToolComponent,
        PanelTitleWithErrorComponent,
    ],
    templateUrl: './uri-encoder.component.html',
    styleUrl: './uri-encoder.component.scss'
})
export class UriEncoderComponent implements OnInit {
  encode = true;
  input = signal<string>('Lorem ipsum dolor sit amet, consectetur adipiscing elit');
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    let output = '';

    try {
      if (this.encode) {
        output = encodeURIComponent(this.input());
      } else {
        output = decodeURIComponent(this.input());
      }
    } catch (e) {
      if (this.encode) {
        this.error = 'Unknown error.';
      } else {
        this.error = 'Invalid data';
      }
      return;
    }


    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      mode: this.encode ? 'encode' : 'decode',
      inputLength: this.input().length,
      outputLength: output.length
    });
  }

  onChangeMode() {
    if (!this.error) {
      const temp = this.input();
      this.input.set(this.output());
      this.output.set(temp);
    }

    this.generate();
  }

  onForceEncode() {
    this.generate();
  }
}
