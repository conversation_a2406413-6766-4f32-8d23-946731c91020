import {Component, inject, OnInit, signal} from '@angular/core';
import {TextOutputComponent} from '@/app/ui/components/text-output/text-output.component';
import {FormsModule} from '@angular/forms';
import {PlayComponent} from '@/app/ui/icons/play/play.component';
import {ToolDescription} from '@common/types/tool-description';
import {IntegerInputComponent} from '@/app/ui/components/integer-input/integer-input.component';
import * as toolInfo from '@common/data/tools/U/uuid-generator.json';
import { CenterPanelToolComponent } from '@/app/ui/layout/center-panel-tool/center-panel-tool.component';
import { MarkdownComponent } from 'ngx-markdown';
import manual from '@common/data/tools/U/uuid-generator.md';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-uuid-generator',
  imports: [
    TextOutputComponent,
    FormsModule,
    PlayComponent,
    IntegerInputComponent,
    CenterPanelToolComponent,
    MarkdownComponent,
  ],
    templateUrl: './uuid-generator.component.html',
    styleUrl: './uuid-generator.component.scss'
})
export class UuidGeneratorComponent implements OnInit {
  output = signal('');
  stats = signal('');
  quantity = 10;
  minQuantity = 1;
  maxQuantity = 1000000;

  protected readonly manual = manual;
  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    const count = Number(this.quantity);

    const result: string[] = [];

    const start = performance.now();
    for(let i = 0; i < count; i++) {
      result.push(crypto.randomUUID());
    }
    const end = performance.now();
    const time = (end - start) / 1000;

    this.stats.set(`Done in ${time.toFixed(2)}s`);
    this.output.set(result.join('\n'));

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.id, {
      quantity: count
    });
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }

}
