import { Component, effect, inject, model, OnInit, signal } from '@angular/core';
import { ToolDescription } from '@common/types/tool-description';
import { FormsModule } from '@angular/forms';
import * as toolInfo from '@common/data/tools/U/url-parser.json';
import { InlineErrorBlockComponent } from '../../../../../ui/components/inline-error-block/inline-error-block.component';
import { CopyInputComponent } from '../../../../../ui/components/copy-input/copy-input.component';
import { CenterPanelToolComponent } from '../../../../../ui/layout/center-panel-tool/center-panel-tool.component';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-url-parser',
    imports: [
        FormsModule,
        InlineErrorBlockComponent,
        CopyInputComponent,
        CenterPanelToolComponent,
    ],
    templateUrl: './url-parser.component.html',
    styleUrl: './url-parser.component.scss'
})
export class UrlParserComponent implements OnInit {
  output = signal('');
  stats = signal('');
  url = model('https://toolsportal.net/contact?from=John#tab-1');
  error = '';
  parsed?: any;
  props = [
    ['protocol', 'Protocol'],
    ['hostname', 'Hostname'],
    ['port', 'Port'],
    ['host', 'Host'],
    ['pathname', 'Pathname'],
    ['search', 'Search'],
    ['hash', 'Hash'],
    ['username', 'Username'],
    ['password', 'Password'],
  ];

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    effect(() => {
      this.url();
      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    this.error = '';
    if (!this.url()) {
      this.error = 'Please provide an URL address to parse';
      return;
    }

    try {
      this.parsed = new URL(this.url());

      // Track tool usage
      this.analyticsService.trackToolUse(this.toolInfo.toolId, {
        urlLength: this.url().length,
        protocol: this.parsed.protocol,
        hasQuery: !!this.parsed.search,
        hasFragment: !!this.parsed.hash
      });
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }
}
