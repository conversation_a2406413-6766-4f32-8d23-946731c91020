import {Component, inject, OnInit, signal} from '@angular/core';
import {TextOutputComponent} from '@/app/ui/components/text-output/text-output.component';
import {FormsModule} from '@angular/forms';
import {PlayComponent} from '@/app/ui/icons/play/play.component';
import {ToolDescription} from '@common/types/tool-description';
import {IntegerInputComponent} from '@/app/ui/components/integer-input/integer-input.component';
import * as toolInfo from '@common/data/tools/U/ulid-generator.json';
import {CenterPanelToolComponent} from '@/app/ui/layout/center-panel-tool/center-panel-tool.component';
import { monotonicFactory, ulid } from 'ulid';
import { MarkdownComponent } from 'ngx-markdown';
import manual from '@common/data/tools/U/ulid-generator.md';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-ulid-generator',
  imports: [
    TextOutputComponent,
    FormsModule,
    PlayComponent,
    IntegerInputComponent,
    CenterPanelToolComponent,
    MarkdownComponent,
  ],
    templateUrl: './ulid-generator.component.html',
    styleUrl: './ulid-generator.component.scss'
})
export class UlidGeneratorComponent implements OnInit {
  output = signal('');
  stats = signal('');
  defaultQuantity = 10;
  quantity = this.defaultQuantity;
  minQuantity = 1;
  maxQuantity = 1000000;
  monotonic = true;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    const count = Number(this.quantity);
    const result: string[] = [];

    const start = performance.now();

    if (this.monotonic) {
      // Use monotonic factory for time-ordered ULIDs
      const factory = monotonicFactory();
      for(let i = 0; i < count; i++) {
        result.push(factory());
      }
    } else {
      // Generate standard ULIDs
      for(let i = 0; i < count; i++) {
        result.push(ulid());
      }
    }

    const end = performance.now();
    const time = (end - start) / 1000;

    this.stats.set(`Done in ${time.toFixed(2)}s`);
    this.output.set(result.join('\n'));

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.id, {
      quantity: count,
      monotonic: this.monotonic
    });
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }

  protected readonly manual = manual;
}
