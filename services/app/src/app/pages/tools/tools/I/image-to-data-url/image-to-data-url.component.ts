import { Component, effect, inject, OnInit, signal } from '@angular/core';
import { TextOutputComponent } from '../../../../../ui/components/text-output/text-output.component';
import { ToolDescription } from '@common/types/tool-description';
import { ImageInputComponent } from '../../../../../ui/components/image-input/image-input.component';
import * as toolInfo from '@common/data/tools/I/image-to-data-url.json';
import prettyBytes from 'pretty-bytes';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { debounceTime, Subject } from 'rxjs';
import { TwoPanelToolComponent } from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import {
  PanelTitleWithErrorComponent
} from '../../../../../ui/components/panel-title-with-error/panel-title-with-error.component';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-image-to-data-url',
    imports: [
        ImageInputComponent,
        TextOutputComponent,
        TwoPanelToolComponent,
        PanelTitleWithErrorComponent
    ],
    templateUrl: './image-to-data-url.component.html',
    styleUrl: './image-to-data-url.component.scss'
})
export class ImageToDataUrlComponent implements OnInit {
  input = signal<File | undefined>(undefined);
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    // this.generate();
  }

  async generate() {
    this.error = '';
    this.stats.set('');
    let output = '';
    const file = this.input();

    if (!file) {
      return;
    }

    try {
      // Convert file to data URL
      output = await this.fileToDataUrl(file);
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }

    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${prettyBytes(this.input()?.size??0)}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      fileSize: file.size,
      fileType: file.type,
      outputLength: output.length
    });
  }

  fileToDataUrl(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

}
