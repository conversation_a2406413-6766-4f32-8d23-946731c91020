import {Component, effect, inject, OnInit, signal} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {TextInputComponent} from '../../../../../ui/components/text-input/text-input.component';
import {TextOutputComponent} from '../../../../../ui/components/text-output/text-output.component';
import {ToolDescription} from '@common/types/tool-description';
import {debounceTime, Subject} from 'rxjs';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
import * as toolInfo from '@common/data/tools/Y/yaml-formatter.json';
import {parseDocument, stringify} from 'yaml';
import { TwoPanelToolComponent } from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import {
  PanelTitleWithErrorComponent
} from '../../../../../ui/components/panel-title-with-error/panel-title-with-error.component';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-yaml',
    imports: [
        FormsModule,
        TextInputComponent,
        TextOutputComponent,
        TwoPanelToolComponent,
        PanelTitleWithErrorComponent
    ],
    templateUrl: './yaml-formatter.component.html',
    styleUrl: './yaml-formatter.component.scss'
})
export class YamlFormatterComponent implements OnInit {
  input = signal<string>(`foo:\nbar:   # here goes ugly comment\n# and some more\n- 42\n- 1: null\n  2: true\n`);
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;
  indent = '2';

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    let output = '';
    const indent = this.getIndent();

    try {
      const doc = parseDocument(this.input());
      output = stringify(doc, null, indent);
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }

    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: this.input().length,
      outputLength: output.length,
      indent: indent
    });
  }

  onForceEncode() {
    this.generate();
  }

  onIndentChange() {
    this.generate();
  }

  private getIndent() {
    if (this.indent === '2') {
      return 2;
    } else {
      return 4;
    }
  }

}
