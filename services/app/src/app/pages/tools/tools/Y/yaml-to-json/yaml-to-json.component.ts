import { Component, effect, inject, OnInit, signal } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TextInputComponent } from '@/app/ui/components/text-input/text-input.component';
import { TextOutputComponent } from '@/app/ui/components/text-output/text-output.component';
import { ToolDescription } from '@common/types/tool-description';
import { debounceTime, Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { parseDocument } from 'yaml';
import * as toolInfo from '@common/data/tools/Y/yaml-to-json.json';
import {
  PanelTitleWithErrorComponent
} from '@/app/ui/components/panel-title-with-error/panel-title-with-error.component';
import { TwoPanelToolComponent } from '@/app/ui/layout/two-panel-tool/two-panel-tool.component';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-yaml-to-json',
    imports: [
        ReactiveFormsModule,
        TextInputComponent,
        TextOutputComponent,
        FormsModule,
        PanelTitleWithErrorComponent,
        TwoPanelToolComponent,
    ],
    templateUrl: './yaml-to-json.component.html',
    styleUrl: './yaml-to-json.component.scss'
})
export class YamlToJsonComponent implements OnInit {
  input = signal<string>('name: John\n' +
    'age: 30\n' +
    'skills:\n' +
    '  - hacker\n' +
    '  - developer\n' +
    '  - blogger');
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;
  indent = '2';

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    let output = '';
    const indent = this.getIndent();

    try {
      const doc = parseDocument(this.input());
      output = JSON.stringify(doc, null, indent);
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }

    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: this.input().length,
      outputLength: output.length,
      indent: indent
    });
  }

  onForceEncode() {
    this.generate();
  }

  onIndentChange() {
    this.generate();
  }

  private getIndent() {
    if (this.indent === '2') {
      return 2;
    } else if (this.indent === '4') {
      return 4;
    } else if (this.indent === 't') {
      return '\t';
    } else {
      return 0;
    }
  }
}
