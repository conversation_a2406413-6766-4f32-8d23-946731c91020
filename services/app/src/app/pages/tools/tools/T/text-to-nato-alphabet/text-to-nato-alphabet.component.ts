import {Component, effect, inject, model, OnInit} from '@angular/core';
import {CenterPanelToolComponent} from '../../../../../ui/layout/center-panel-tool/center-panel-tool.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {ToolDescription} from '@common/types/tool-description';
import * as toolInfo from '@common/data/tools/T/text-to-nato-alphabet.json';
import { AnalyticsService } from '@/app/services/analytics.service';


@Component({
    selector: 'app-nato-alphabet',
    imports: [
        CenterPanelToolComponent,
        ReactiveFormsModule,
        FormsModule,
    ],
    templateUrl: './text-to-nato-alphabet.component.html',
    styleUrl: './text-to-nato-alphabet.component.scss'
})
export class TextToNatoAlphabetComponent implements OnInit {
  input = model('Hello World');
  output = '';

  natoPhoneticAlphabet: Record<string, string> = {
    'A': 'Alpha',
    'B': 'Bravo',
    'C': '<PERSON>',
    'D': 'Delta',
    'E': 'Echo',
    'F': 'Foxtrot',
    'G': 'Golf',
    'H': 'Hotel',
    'I': 'India',
    'J': 'Juliette',
    'K': 'Kilo',
    'L': 'Lima',
    'M': 'Mike',
    'N': 'November',
    'O': 'Oscar',
    'P': 'Papa',
    'Q': 'Quebec',
    'R': 'Romeo',
    'S': 'Sierra',
    'T': 'Tango',
    'U': 'Uniform',
    'V': 'Victor',
    'W': 'Whiskey',
    'X': 'Xray',
    'Y': 'Yankee',
    'Z': 'Zulu',
  } as const;

  left = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M'];
  right = ['N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
  indices = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];


  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    effect(() => {
      this.input();
      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    const text = this.input();

    this.output = '';
    const outputArray: string[] = [];
    for(let i = 0; i < text.length; i++) {
      const char = text[i].toUpperCase();
      if (char in this.natoPhoneticAlphabet) {
        outputArray.push(this.natoPhoneticAlphabet[char]);
      } else {
        outputArray.push(char);
      }
    }

    this.output = outputArray.join(' ');

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: text.length,
      outputLength: this.output.length,
      alphabeticChars: outputArray.filter(item => item in this.natoPhoneticAlphabet).length
    });
  }

  hasSpeach = 'speechSynthesis' in window;
  speaking = false;
  onSpeak(text: string) {
    if (this.speaking) {
      return;
    }

    this.speaking = true;
    const synth = window.speechSynthesis;

    const voices = [...synth.getVoices()];
    // voices.sort((a: SpeechSynthesisVoice, b: SpeechSynthesisVoice) => {
    //   const aname = a.name.toUpperCase();
    //   const bname = b.name.toUpperCase();
    //
    //   return aname.localeCompare(bname);
    // });

    console.log(voices.length);

    const utter = new SpeechSynthesisUtterance(text);
    // utter.voice = voices[Math.floor(Math.random() * voices.length)];
    utter.rate = .50;
    utter.onend = () => { this.speaking = false; }
    utter.onerror = (e) => {
      console.log('Speach error:', e);
      this.speaking = false;
    }

    synth.speak(utter);
  }

  protected readonly speechSynthesis = speechSynthesis;
}
