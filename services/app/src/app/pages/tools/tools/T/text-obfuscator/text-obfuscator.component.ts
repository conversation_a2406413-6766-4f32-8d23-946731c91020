import {Component, effect, inject, model, OnInit} from '@angular/core';
import {CenterPanelToolComponent} from '../../../../../ui/layout/center-panel-tool/center-panel-tool.component';
import {CopyInputComponent} from '../../../../../ui/components/copy-input/copy-input.component';
import {InlineErrorBlockComponent} from '../../../../../ui/components/inline-error-block/inline-error-block.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {ToolDescription} from '@common/types/tool-description';
import * as toolInfo from '@common/data/tools/T/text-obfuscator.json';
import {IntegerInputComponent} from '../../../../../ui/components/integer-input/integer-input.component';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-text-obfuscator',
    imports: [
        CenterPanelToolComponent,
        CopyInputComponent,
        InlineErrorBlockComponent,
        ReactiveFormsModule,
        FormsModule,
        IntegerInputComponent,
    ],
    templateUrl: './text-obfuscator.component.html',
    styleUrl: './text-obfuscator.component.scss'
})
export class TextObfuscatorComponent implements OnInit {
  stats = '';
  input = model('Internationalization is very difficult');
  keepLeft = model(4);
  keepRight = model(4);
  keepSpaces = model(true);
  replaceMethod = model<'keep' | 'fixed'>('keep');
  replaceCharacter = model('*');
  error = '';
  output = '';

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    effect(() => {
      this.input();
      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    this.error = '';
    const text = this.input();

    this.output = '';

    const len = text.length;
    const keepLeft = this.keepLeft();
    const keepRight = this.keepRight();
    const keepSpaces = this.keepSpaces();
    const replaceMethod = this.replaceMethod();
    const replaceCharacter = this.replaceCharacter();

    if (len > keepLeft + keepRight) {
      const left = text.slice(0, keepLeft);
      const right = keepRight ? text.slice(-keepRight) : '';

      let middle = ''
      if (replaceMethod === 'keep') {
        for (let i = keepLeft; i < len - keepRight; i++) {
          if (text[i] === ' ' && keepSpaces) {
            middle += ' '
          } else {
            middle += replaceCharacter;
          }
        }
      } else {
        middle = replaceCharacter.repeat(3);
      }

      this.output = left + middle + right
    } else {
      this.error = 'Text is too short to obfuscate';
      this.output = text;
    }

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: text.length,
      keepLeft: keepLeft,
      keepRight: keepRight,
      replaceMethod: replaceMethod,
      keepSpaces: keepSpaces
    });
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }
}

