import { Component, effect, inject, signal } from '@angular/core';
import {
  PanelTitleWithErrorComponent
} from '../../../../../ui/components/panel-title-with-error/panel-title-with-error.component';
import { ReactiveFormsModule } from '@angular/forms';
import { TextInputComponent } from '../../../../../ui/components/text-input/text-input.component';
import { TextOutputComponent } from '../../../../../ui/components/text-output/text-output.component';
import { TwoPanelToolComponent } from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import * as toolInfo from '@common/data/tools/T/toml-formatter.json';
import { ToolDescription } from '@common/types/tool-description';
import { debounceTime, Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import type { parse as TomlParse, stringify as TomlStringify } from 'smol-toml';
import { AnalyticsService } from '@/app/services/analytics.service';


@Component({
    selector: 'app-toml',
    imports: [
        PanelTitleWithErrorComponent,
        ReactiveFormsModule,
        TextInputComponent,
        TextOutputComponent,
        TwoPanelToolComponent
    ],
    templateUrl: './toml-formatter.component.html',
    styleUrl: './toml-formatter.component.scss'
})
export class TomlFormatterComponent {
  input = signal<string>(`object="readable but long text that serves no apparent purpose"
array=[
1, 2, 3, 4, 5, 6, 7, 8,
9, 0 ]


[complex]
hello = "Dave"
enabled = true`);
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  private parse?: typeof TomlParse;
  private stringify?: typeof TomlStringify;


  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    import('smol-toml').then((toml) => {
      this.parse = toml.parse;
      this.stringify = toml.stringify;
      this.generate();
    });
  }

  generate() {
    if (!this.parse || !this.stringify) {
      return;
    }

    let output = '';

    try {
      output = this.stringify(this.parse(this.input()));
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }

    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: this.input().length,
      outputLength: output.length
    });
  }

  onForceEncode() {
    this.generate();
  }
}
