import {Component, effect, inject, model, OnInit} from '@angular/core';
import {CenterPanelToolComponent} from '../../../../../ui/layout/center-panel-tool/center-panel-tool.component';
import {InlineErrorBlockComponent} from '../../../../../ui/components/inline-error-block/inline-error-block.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {ToolDescription} from '@common/types/tool-description';
import * as toolInfo from '@common/data/tools/T/text-statistics.json';
import {createEmptyResult, textStats, WritingStatsResult} from '../../../../../lib/words';
import {PrettyBytesPipe} from '../../../../../ui/pipes/pretty-bytes.pipe';
import { AnalyticsService } from '@/app/services/analytics.service';


@Component({
    selector: 'app-text-stats',
    imports: [
        CenterPanelToolComponent,
        InlineErrorBlockComponent,
        ReactiveFormsModule,
        FormsModule,
        PrettyBytesPipe,
    ],
    templateUrl: './text-stats.component.html',
    styleUrl: './text-stats.component.scss'
})
export class TextStatsComponent implements OnInit {
  stats = '';
  input = model('Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.');
  error = '';
  output: WritingStatsResult = createEmptyResult();


  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    effect(() => {
      this.input();
      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    this.error = '';
    const text = this.input();

    const start = performance.now();
    this.output = createEmptyResult();
    try {
      this.output = textStats(text);
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }

    const end = performance.now();
    this.stats = `Hashing took ${(end - start).toFixed(2)} ms`;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      textLength: text.length,
      wordCount: this.output.wordsCount,
      paragraphCount: this.output.paragraphCount
    });
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }

}

