import {Component, effect, inject, OnInit, signal} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {TextOutputComponent} from '../../../../../ui/components/text-output/text-output.component';
import {ToolDescription} from '@common/types/tool-description';
import * as toolInfo from '@common/data/tools/B/base64.json';
import {TextInputComponent} from '../../../../../ui/components/text-input/text-input.component';
import {debounceTime, Subject} from 'rxjs';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
import {ExclamationTriangleComponent} from '../../../../../ui/icons/exclamation-triangle/exclamation-triangle.component';
import { InlineErrorBlockComponent } from '../../../../../ui/components/inline-error-block/inline-error-block.component';
import { TwoPanelToolComponent } from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import { AnalyticsService } from '@/app/services/analytics.service';

function base64ToBytes(encoded: string) {
  const binString = atob(encoded);
  const data = Uint8Array.from(binString as ArrayLike<any>, (m) => m.codePointAt(0));
  return new TextDecoder().decode(data);
}

function bytesToBase64(text: string) {
  const bytes = new TextEncoder().encode(text);
  const binString = Array.from(bytes, (byte) =>
    String.fromCodePoint(byte),
  ).join("");
  return btoa(binString);
}


@Component({
    selector: 'app-base64',
    imports: [
        FormsModule,
        TextOutputComponent,
        TextInputComponent,
        ExclamationTriangleComponent,
        InlineErrorBlockComponent,
        TwoPanelToolComponent
    ],
    templateUrl: './base64.component.html',
    styleUrl: './base64.component.scss'
})
export class Base64Component implements OnInit {
  binary = false;
  encode = true;
  input = signal<string>('Lorem ipsum dolor sit amet, consectetur adipiscing elit');
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;

  wrap76 = false;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    let output = '';

    try {
      // if (this.encode) {
      //   if (this.binary) {
      //     output = btoa(new TextEncoder().encode(this.input()).reduce((acc, c) => acc + String.fromCharCode(c), ''));
      //   } else {
      //     output = btoa(this.input());
      //   }
      // } else {
      //   if (this.binary) {
      //     output = new TextDecoder('utf-8').decode(new Uint8Array(atob(this.input()).split('').map(c => c.charCodeAt(0))));
      //   } else {
      //     output = atob(this.input());
      //   }
      // }
      if (this.encode) {
        output = bytesToBase64(this.input());

        if (this.wrap76) {
          // split output into 76 character lines
          const lines = [];
          for (let i = 0; i < output.length; i += 76) {
            lines.push(output.slice(i, i + 76));
          }

          output = lines.join('\n');
        }

      } else {
        output = base64ToBytes(this.input());
      }
    } catch (e) {
      // console.log(e);
      if (this.encode) {
        this.error = 'Base64 cannot encode unicode characters. Use binary mode instead.';
      } else {
        this.error = 'Invalid base64 data';
      }
      return;
    }


    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      mode: this.encode ? 'encode' : 'decode',
      inputLength: this.input().length,
      outputLength: output.length,
      wrap76: this.wrap76
    });
  }

  onChangeMode() {
    if (!this.error) {
      const temp = this.input();
      this.input.set(this.output());
      this.output.set(temp);
    }

    this.generate();
  }

  onForceEncode() {
    this.generate();
  }
}
