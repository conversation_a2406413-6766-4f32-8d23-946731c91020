import {Component, effect, inject, OnInit, signal} from '@angular/core';
import {TextInputComponent} from '../../../../../ui/components/text-input/text-input.component';
import {TextOutputComponent} from '../../../../../ui/components/text-output/text-output.component';
import {ToolDescription} from '@common/types/tool-description';
import {debounceTime, Subject} from 'rxjs';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
import * as toolInfo from '@common/data/tools/J/json-formatter.json';
import {FormsModule} from '@angular/forms';
import { TwoPanelToolComponent } from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import {
  PanelTitleWithErrorComponent
} from '../../../../../ui/components/panel-title-with-error/panel-title-with-error.component';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-json',
    imports: [
        TextInputComponent,
        TextOutputComponent,
        FormsModule,
        TwoPanelToolComponent,
        PanelTitleWithErrorComponent
    ],
    templateUrl: './json-formatter.component.html',
    styleUrl: './json-formatter.component.scss'
})
export class JsonFormatterComponent implements OnInit {
  input = signal<string>(`{"object":"not so readable",\t\t"array":[1,2,3,4,5,6,7,8,9,0]}`);
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;
  indent = '2';

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    let output = '';
    const indent = this.getIndent();

    try {
      output = JSON.stringify(JSON.parse(this.input()), null, indent);
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }

    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: this.input().length,
      outputLength: output.length,
      indent: indent
    });
  }

  onForceEncode() {
    this.generate();
  }

  onIndentChange() {
    this.generate();
  }

  private getIndent() {
    if (this.indent === '2') {
      return 2;
    } else if (this.indent === '4') {
      return 4;
    } else if (this.indent === 't') {
      return '\t';
    } else {
      return 0;
    }
  }

}
