import {Component, effect, inject, signal} from '@angular/core';
import {ReactiveFormsModule} from '@angular/forms';
import {debounceTime, Subject} from 'rxjs';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
// @ts-ignore
import stringify from 'dotenv-stringify';
import {
  PanelTitleWithErrorComponent
} from '../../../../../ui/components/panel-title-with-error/panel-title-with-error.component';
import {TextInputComponent} from '../../../../../ui/components/text-input/text-input.component';
import {TextOutputComponent} from '../../../../../ui/components/text-output/text-output.component';
import {TwoPanelToolComponent} from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import * as toolInfo from '@common/data/tools/J/json-to-dotenv.json';
import {ToolDescription} from '@common/types/tool-description';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-json-to-dotenv',
    imports: [
        PanelTitleWithErrorComponent,
        ReactiveFormsModule,
        TextInputComponent,
        TextOutputComponent,
        TwoPanelToolComponent
    ],
    templateUrl: './json-to-dotenv.component.html',
    styleUrl: './json-to-dotenv.component.scss'
})
export class JsonToDotenvComponent {
  input = signal<string>(`
  {
    "HOST": "localhost",
    "PORT": 3000,
    "SECRET": "This is \\"THE\\" secret",
    "PROP": "John's data",
    "JSON": {"enabled": true, "code": "1234"},
    "PADDED": "    padded    "
    }
  `);
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    let output = '';

    try {
      const doc = JSON.parse(this.input());
      output = stringify(doc);
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }

    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: this.input().length,
      outputLength: output.length
    });
  }

  onForceEncode() {
    this.generate();
  }

}
