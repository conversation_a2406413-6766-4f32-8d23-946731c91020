import { Component, effect, inject, OnInit, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TextInputComponent } from '@/app/ui/components/text-input/text-input.component';
import { TextOutputComponent } from '@/app/ui/components/text-output/text-output.component';
import { TwoPanelToolComponent } from '@/app/ui/layout/two-panel-tool/two-panel-tool.component';
import { ToolDescription } from '@common/types/tool-description';
import { debounceTime, Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import * as toolInfo from '@common/data/tools/J/jwt-decoder.json';
import { PanelTitleWithErrorComponent } from '@/app/ui/components/panel-title-with-error/panel-title-with-error.component';
import { NgClass } from '@angular/common';
import * as jose from 'jose';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-jwt-decoder',
    imports: [
        FormsModule,
        TextInputComponent,
        TextOutputComponent,
        TwoPanelToolComponent,
        PanelTitleWithErrorComponent,
        NgClass
    ],
    templateUrl: './jwt-decoder.component.html',
    styleUrl: './jwt-decoder.component.scss'
})
export class JwtDecoderComponent implements OnInit {
  input = signal<string>('');
  headerOutput = signal<string>('');
  payloadOutput = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;
  secretKey = signal<string>('');
  validationResult: boolean | null = null;
  validationMessage = '';

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    effect(() => {
      // Automatically validate when secret key changes
      this.secretKey();
      if (this.input().trim()) {
        this.validateSignature();
      }
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.decode();
      // Also validate when token changes
      if (this.secretKey().trim()) {
        this.validateSignature();
      }
    });
  }

  ngOnInit(): void {
    // Set a sample JWT token
    this.input.set('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c');
    this.decode();
  }

  decode() {
    try {
      const token = this.input().trim();
      if (!token) {
        this.error = '';
        this.headerOutput.set('');
        this.payloadOutput.set('');
        return;
      }

      const parts = token.split('.');

      if (parts.length !== 3) {
        this.error = 'Invalid JWT token format. Expected 3 parts separated by dots.';
        this.headerOutput.set('');
        this.payloadOutput.set('');
        return;
      }

      // Decode header
      try {
        const headerJson = jose.decodeProtectedHeader(token);
        this.headerOutput.set(JSON.stringify(headerJson, null, 2));
      } catch (e) {
        this.error = 'Invalid header format: ' + (e as Error).message;
        this.headerOutput.set('');
        return;
      }

      // Decode payload
      try {
        const payloadJson = jose.decodeJwt(token);
        this.payloadOutput.set(JSON.stringify(payloadJson, null, 2));
      } catch (e) {
        this.error = 'Invalid payload format: ' + (e as Error).message;
        this.payloadOutput.set('');
        return;
      }

      this.error = '';
      this.stats.set(`JWT token with ${parts.length} parts`);
      this.inputSize.set(`Size: ${this.input().length} characters`);
      this.inputDirty = false;

      // Track tool usage
      const header = jose.decodeProtectedHeader(token);
      this.analyticsService.trackToolUse(this.toolInfo.toolId, {
        tokenLength: token.length,
        algorithm: header.alg,
        hasSecretKey: !!this.secretKey().trim()
      });
    } catch (e) {
      this.error = (e as Error).message;
      this.headerOutput.set('');
      this.payloadOutput.set('');
    }
  }

  onForceEncode() {
    this.decode();
  }

  async validateSignature() {
    this.validationResult = null;
    this.validationMessage = '';

    try {
      const token = this.input().trim();
      // base64 secret
      const secret = this.secretKey().trim();

      if (!token || !secret) {
        // Reset validation if either token or secret is empty
        return;
      }

      const header = jose.decodeProtectedHeader(token);
      if (header.alg?.startsWith('RS')) {
        const publicKey = await jose.importSPKI(secret, header.alg)
        const result = await jose.jwtVerify(token, publicKey);
        this.validationResult = true;
        this.validationMessage = 'Token signature validated successfully with the provided public key.';
      } else if (header.alg?.startsWith('HS')) {
        const result = await jose.jwtVerify(token, new TextEncoder().encode(secret));
        this.validationResult = true;
        this.validationMessage = 'Token signature validated successfully with the provided secret key.';
      } else {
        this.validationResult = false;
        this.validationMessage = 'Unsupported signature algorithm: ' + header.alg;
        return;
      }
    } catch (e) {
      this.validationResult = false;
      this.validationMessage = 'Validation failed!';
    }
  }
}
