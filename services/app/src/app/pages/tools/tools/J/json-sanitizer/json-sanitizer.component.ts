import {Component, effect, inject, OnInit, signal} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {TextInputComponent} from '../../../../../ui/components/text-input/text-input.component';
import {TextOutputComponent} from '../../../../../ui/components/text-output/text-output.component';
import {ToolDescription} from '@common/types/tool-description';
import {debounceTime, Subject} from 'rxjs';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
import * as toolInfo from '@common/data/tools/J/json-sanitizer.json';
import JSON5 from 'json5';
import { TwoPanelToolComponent } from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import {
  PanelTitleWithErrorComponent
} from '../../../../../ui/components/panel-title-with-error/panel-title-with-error.component';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-json-sanitizer',
    imports: [
        FormsModule,
        TextInputComponent,
        TextOutputComponent,
        TwoPanelToolComponent,
        PanelTitleWithErrorComponent
    ],
    templateUrl: './json-sanitizer.component.html',
    styleUrl: './json-sanitizer.component.scss'
})
export class JsonSanitizerComponent implements OnInit {
  input = signal<string>(`{\nnot: 'So',\n'clean': ['J', 'S', 'O', 'N',NaN, Infinity],\nobject: .1,\n} // And with comments`);
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    let output = '';

    try {
      output = JSON.stringify(JSON5.parse(this.input()), null, 2);
    } catch (e) {
      const msg = (e as Error).message;
      // Cleanup JSON5 from error message.
      if (msg.startsWith('JSON5: ')) {
        this.error = msg.substring(7);
        // capitalize first letter
        this.error = this.error.charAt(0).toUpperCase() + this.error.slice(1);
      } else {
        this.error = msg;
      }
      return;
    }

    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: this.input().length,
      outputLength: output.length
    });
  }

  onForceEncode() {
    this.generate();
  }

}
