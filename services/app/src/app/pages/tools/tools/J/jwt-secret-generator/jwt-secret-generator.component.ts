import {Component, inject, OnInit, signal} from '@angular/core';
import {CenterPanelToolComponent} from '../../../../../ui/layout/center-panel-tool/center-panel-tool.component';
import {FormsModule} from '@angular/forms';
import {PlayComponent} from '../../../../../ui/icons/play/play.component';
import {TextOutputComponent} from '../../../../../ui/components/text-output/text-output.component';
import {ToolDescription} from '@common/types/tool-description';
import * as toolInfo from '@common/data/tools/J/jwt-secret-generator.json';
import {ClickRestoreDirective} from '../../../../../ui/components/click-restore.directive';
import {WheelNumberDirective} from '../../../../../ui/components/wheel-number.directive';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-jwt-secret',
    imports: [
        CenterPanelToolComponent,
        FormsModule,
        PlayComponent,
        TextOutputComponent,
        ClickRestoreDirective,
        WheelNumberDirective,
    ],
    templateUrl: './jwt-secret-generator.component.html',
    styleUrl: './jwt-secret-generator.component.scss'
})
export class JwtSecretGeneratorComponent implements OnInit {
  output = signal('');
  stats = signal('');
  defaultLength = 8;
  length = this.defaultLength;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    // console.log(`Generating ${this.quantity} Nano IDs with length ${this.length}`);
    const count = Number(this.length);

    const buffer = new Uint32Array(count);
    crypto.getRandomValues(buffer);
    const result = Array.from(buffer, (value) => value.toString(16).padStart(8, '0')).join('');


    this.stats.set(`Length ${result.length} bytes`);
    this.output.set(result);

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      length: count,
      outputLength: result.length
    });
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }
}
