import { Component, computed, inject, linkedSignal, model, OnInit } from '@angular/core';
import { FullPanelToolComponent } from '../../../../../ui/layout/full-panel-tool/full-panel-tool.component';
import { ToolDescription } from '@common/types/tool-description';
import { loremIpsumGenerator } from './lorem-ipsum-generator';
import toolInfo from '@common/data/tools/L/lorem-ipsum-generator.json';
import { FormsModule } from '@angular/forms';
import { TextOutputComponent } from '../../../../../ui/components/text-output/text-output.component';
import { format } from 'date-fns';
import { AnalyticsService } from '@/app/services/analytics.service';


@Component({
  selector: 'app-lorem-ipsum-generator',
  imports: [
    FormsModule,
    FullPanelToolComponent,
    TextOutputComponent,
  ],
  templateUrl: './lorem-ipsum-generator.component.html',
  styleUrl: './lorem-ipsum-generator.component.scss'
})
export class LoremIpsumGeneratorComponent implements OnInit {
  stats = '';
  paragraphs = model(2);
  sentencesPerParagraphMin = model(3);
  sentencesPerParagraphMax = linkedSignal<number, number>({
    source: this.sentencesPerParagraphMin,
    computation: (source, previous,) => {
      if (!previous) {
        return source + 7;
      }

      if (previous.value < source) {
        return source;
      }

      return previous.value;
    }
  });
  wordsPerSentenceMin = model(5);
  wordsPerSentenceMax = linkedSignal<number, number>({
    source: this.wordsPerSentenceMin,
    computation: (source, previous,) => {
      if (!previous) {
        return source + 7;
      }

      if (previous.value < source) {
        return source;
      }

      return previous.value;
    }
  });
  startWithLoremIpsum = model(true);
  asHtml = model(false);
  htmlPretty = model(false);

  fileName = computed(() => {
    return `lorem-ipsum-${format(new Date(), 'yyyy-MM-dd')}.${this.asHtml() ? 'html' : 'txt'}`;
  });

  output = computed(() => {
    const result = loremIpsumGenerator({
      paragraphs: this.paragraphs(),
      sentencesPerParagraph: {min: this.sentencesPerParagraphMin(), max: this.sentencesPerParagraphMax()},
      wordsPerSentence: {min: this.wordsPerSentenceMin(), max: this.wordsPerSentenceMax()},
      startWithLoremIpsum: this.startWithLoremIpsum(),
      html: this.asHtml(),
      htmlPretty: this.htmlPretty(),
    });

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      paragraphs: this.paragraphs(),
      asHtml: this.asHtml(),
      startWithLoremIpsum: this.startWithLoremIpsum(),
      outputLength: result.length
    });

    return result;
  });

  error = '';

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {

  }

  ngOnInit(): void {
  }
}
