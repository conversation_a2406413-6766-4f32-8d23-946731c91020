import { Component, effect, inject, OnInit, signal } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TextInputComponent } from '../../../../../ui/components/text-input/text-input.component';
import { TextOutputComponent } from '../../../../../ui/components/text-output/text-output.component';
import { ToolDescription } from '@common/types/tool-description';
import { debounceTime, Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import * as toolInfo from '@common/data/tools/L/latex-to-unicode.json';
import { latexToUnicode } from './latex-to-unicode';
import { TwoPanelToolComponent } from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import {
  PanelTitleWithErrorComponent
} from '../../../../../ui/components/panel-title-with-error/panel-title-with-error.component';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-latex-to-unicode',
    imports: [
        FormsModule,
        ReactiveFormsModule,
        TextInputComponent,
        TextOutputComponent,
        TwoPanelToolComponent,
        PanelTitleWithErrorComponent
    ],
    templateUrl: './latex-to-unicode.component.html',
    styleUrl: './latex-to-unicode.component.scss'
})
export class LatexToUnicodeComponent  implements OnInit {
  input = signal<string>(`\\sum_{i=0}^{n} i^2 = \\frac{(n^2+n)(2n+1)}{6}`);
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    let output = '';

    try {
      output = latexToUnicode(this.input());
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }

    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: this.input().length,
      outputLength: output.length
    });
  }

  onForceEncode() {
    this.generate();
  }


}
