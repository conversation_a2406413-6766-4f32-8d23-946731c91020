import { Component, effect, inject, OnInit, signal } from '@angular/core';
import { ExclamationTriangleComponent } from '../../../../../ui/icons/exclamation-triangle/exclamation-triangle.component';
import { InlineErrorBlockComponent } from '../../../../../ui/components/inline-error-block/inline-error-block.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TextInputComponent } from '../../../../../ui/components/text-input/text-input.component';
import { TextOutputComponent } from '../../../../../ui/components/text-output/text-output.component';
import { TwoPanelToolComponent } from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import { ToolDescription } from '@common/types/tool-description';
import { debounceTime, Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import * as toolInfo from '@common/data/tools/Q/quoted-printable.json';
import * as quotedPrintable from 'quoted-printable';
import * as utf8 from 'utf8';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-quoted-printable',
    imports: [
        FormsModule,
        ExclamationTriangleComponent,
        InlineErrorBlockComponent,
        ReactiveFormsModule,
        TextInputComponent,
        TextOutputComponent,
        TwoPanelToolComponent
    ],
    templateUrl: './quoted-printable.component.html',
    styleUrl: './quoted-printable.component.scss'
})
export class QuotedPrintableComponent implements OnInit {
  binary = false;
  encode = true;
  input = signal<string>('This is some text\nvalue="😊"\n======\tEND\t======\nThat is it!');
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    let output = '';

    try {
      // if (this.encode) {
      //   if (this.binary) {
      //     output = btoa(new TextEncoder().encode(this.input()).reduce((acc, c) => acc + String.fromCharCode(c), ''));
      //   } else {
      //     output = btoa(this.input());
      //   }
      // } else {
      //   if (this.binary) {
      //     output = new TextDecoder('utf-8').decode(new Uint8Array(atob(this.input()).split('').map(c => c.charCodeAt(0))));
      //   } else {
      //     output = atob(this.input());
      //   }
      // }
      if (this.encode) {
        output = quotedPrintable.encode(utf8.encode(this.input()));
      } else {
        output = utf8.decode(quotedPrintable.decode(this.input()));
      }
    } catch (e) {
      // console.log(e);
      if (this.encode) {
        this.error = 'Failed to encode data';
      } else {
        this.error = 'Invalid quoted-printable data';
      }
      return;
    }


    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      mode: this.encode ? 'encode' : 'decode',
      inputLength: this.input().length,
      outputLength: output.length
    });
  }

  onChangeMode() {
    if (!this.error) {
      const temp = this.input();
      this.input.set(this.output());
      this.output.set(temp);
    }

    this.generate();
  }

  onForceEncode() {
    this.generate();
  }

}
