import { Component, effect, inject, model, OnInit, signal } from '@angular/core';
import { ToolDescription } from '@common/types/tool-description';
import { FormsModule } from '@angular/forms';
import * as toolInfo from '@common/data/tools/Q/qrcode-generator.json';
import { InlineErrorBlockComponent } from '@/app/ui/components/inline-error-block/inline-error-block.component';
import { CenterPanelToolComponent } from '@/app/ui/layout/center-panel-tool/center-panel-tool.component';
import { TextInputComponent } from '@/app/ui/components/text-input/text-input.component';
import qrcode from 'qrcode';
import { ClickRestoreDirective } from '@/app/ui/components/click-restore.directive';
import { WheelNumberDirective } from '@/app/ui/components/wheel-number.directive';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
  selector: 'app-qrcode-generator',
  imports: [
    FormsModule,
    InlineErrorBlockComponent,
    CenterPanelToolComponent,
    TextInputComponent,
    ClickRestoreDirective,
    WheelNumberDirective
  ],
  templateUrl: './qrcode-generator.component.html',
  styleUrl: './qrcode-generator.component.scss'
})
export class QrcodeGeneratorComponent implements OnInit {
  input = model('https://powerdev.tools');
  output = signal<string>('');
  error = '';
  size = model(300);
  minSize = 100;
  maxSize = 1000;
  defaultSize = 300;

  errorCorrectionLevel = model<'L' | 'M' | 'Q' | 'H'>('M');
  errorCorrectionLevels = [
    { value: 'L', label: 'Low (7%)' },
    { value: 'M', label: 'Medium (15%)' },
    { value: 'Q', label: 'Quartile (25%)' },
    { value: 'H', label: 'High (30%)' }
  ];

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    effect(() => {
      this.input();
      this.size();
      this.errorCorrectionLevel();
      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  async generate() {
    if (!this.input()) {
      this.error = 'Please enter some text or URL';
      return;
    }

    try {
      const url = await this.generateQRCode(this.input(), {
        errorCorrectionLevel: this.errorCorrectionLevel(),
        width: this.size(),
        margin: 1
      });

      this.error = '';
      this.output.set(url);

      // Track tool usage
      this.analyticsService.trackToolUse(this.toolInfo.toolId, {
        inputLength: this.input().length,
        size: this.size(),
        errorCorrectionLevel: this.errorCorrectionLevel()
      });
    } catch (e) {
      this.error = 'Failed to generate QR code: ' + (e as Error).message;
    }
  }

  /**
   * Generates a QR code as a data URL
   * @param text The text or URL to encode
   * @param options QR code generation options
   * @returns Promise with the generated QR code data URL
   */
  private generateQRCode(text: string, options: qrcode.QRCodeToDataURLOptions): Promise<string> {
    return new Promise((resolve, reject) => {
      qrcode.toDataURL(text, options, (err, url) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(url);
      });
    });
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }

  downloadQRCode() {
    const link = document.createElement('a');
    link.download = 'qrcode.png';
    link.href = this.output();
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
