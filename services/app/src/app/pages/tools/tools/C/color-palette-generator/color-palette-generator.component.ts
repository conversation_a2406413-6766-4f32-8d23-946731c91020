import {Component, effect, inject, model, OnInit} from '@angular/core';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {ToolDescription} from '@common/types/tool-description';
import * as toolInfo from '@common/data/tools/C/color-palette-generator.json';

// @ts-expect-error No typings available
import ColorScheme from 'color-scheme';
import {FullPanelToolComponent} from '../../../../../ui/layout/full-panel-tool/full-panel-tool.component';
import {
  ColorSwatchItemComponent
} from '../../../../../ui/components/color/color-swatch-item/color-swatch-item.component';
import {ColorInputComponent} from '../../../../../ui/components/color/color-input/color-input.component';
import {WheelNumberDirective} from '../../../../../ui/components/wheel-number.directive';
import {ClickRestoreDirective} from '../../../../../ui/components/click-restore.directive';
import {clamp} from '../../../../../utils/math';
import {pickRandomColor} from '../../../../../utils/test-palettes';
import { AnalyticsService } from '@/app/services/analytics.service';

type ColorPalette = {
  name: string,
  colors: string[],
}

@Component({
    selector: 'app-color-palette-generator',
    imports: [
        ReactiveFormsModule,
        FullPanelToolComponent,
        FormsModule,
        ColorSwatchItemComponent,
        ColorInputComponent,
        WheelNumberDirective,
        ClickRestoreDirective
    ],
    templateUrl: './color-palette-generator.component.html',
    styleUrl: './color-palette-generator.component.scss'
})
export class ColorPaletteGeneratorComponent  implements OnInit {
  defaultDistance = 90;
  minDistance = 0;
  maxDistance = 179;

  stats = '';
  input = model('Hello World');
  startColor = model('#f0c030')
  scheme = model('quick');
  distance = model(this.defaultDistance);
  error = '';
  output: ColorPalette[] = [];

  bgColor: string | null = null;

  variations: Record<string, string> = {
    default: 'Base',
    pastel: 'Pastel',
    soft: 'Soft',
    light: 'Light',
    hard: 'Hard',
    pale: 'Pale',
  }

  schemas = [
    {
      value: 'quick',
      label: 'Quick Pick'
    },
    {
      value: 'triade',
      label: 'Triade'
    },
    {
      value: 'tetrade',
      label: 'Tetrade'
    },
    {
      value: 'analogic',
      label: 'Analogic'
    },
    {
      value: 'contrast',
      label: 'Contrast'
    },
    {
      value: 'mono',
      label: 'Monochromatic'
    }
  ]


  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    this.startColor.set(pickRandomColor());

    effect(() => {
      this.input();
      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    this.error = '';
    this.output = [];
    const useSchema = this.scheme();
    const distance = this.distance() / 180;
    const color = this.startColor().substring(1);

    // const palette = new ColorPalette({
    //   steps: 10
    // });
    //
    // this.output = [];
    // palette.palette.forEach((color, index) => {
    //   this.output.push(color.toStringHexRGB());
    // });

    const scheme = new ColorScheme();
    // const base = scheme.from_hue(21)
    const base = scheme.from_hex(color)
      .distance(distance);

    if (useSchema === 'quick') {
      const mode = base.scheme('analogic').add_complement(true);
      for (const [key, value] of Object.entries(this.variations)) {
        this.output.push({
          name: value,
          colors: mode.variation(key).colors()
            .filter((c: string, index:number) => index % 4 === 0)
            .map((color: string) => '#' + color)
        });
      }
    } else {
      let mode = base.scheme(useSchema);
      if (useSchema === 'analogic') {
        mode = mode.add_complement(true);
      }

      for (const [key, value] of Object.entries(this.variations)) {
        this.output.push({
          name: value,
          colors: mode.variation(key).colors().map((color: string) => '#' + color)
        });
      }
    }

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      scheme: useSchema,
      distance: this.distance(),
      startColor: this.startColor(),
      paletteCount: this.output.length,
      totalColors: this.output.reduce((sum, palette) => sum + palette.colors.length, 0)
    });
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }

  onCopy(color: string) {
    navigator.clipboard.writeText(color);
  }

  onWheelDelta($event: number) {
    this.distance.set(clamp(this.distance() + $event, this.minDistance, this.maxDistance));
  }
}

