import {Component, inject, model, OnInit} from '@angular/core';
import {ColorInputComponent} from '@/app/ui/components/color/color-input/color-input.component';
import {
  ColorSwatchItemComponent
} from '@/app/ui/components/color/color-swatch-item/color-swatch-item.component';
import {FullPanelToolComponent} from '@/app/ui/layout/full-panel-tool/full-panel-tool.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {ToolDescription} from '@common/types/tool-description';
import * as toolInfo from '@common/data/tools/C/color-contrast.json';
import {colorContrastChecker, ColorContrastResult} from './contrast-tool';
import {DecimalPipe} from '@angular/common';
import {pickRandomColor, pickRandomPalette} from '@/app/utils/test-palettes';
import { AnalyticsService } from '@/app/services/analytics.service';


@Component({
    selector: 'app-color-contrast',
    imports: [
        ColorInputComponent,
        ColorSwatchItemComponent,
        FullPanelToolComponent,
        ReactiveFormsModule,
        FormsModule,
        DecimalPipe,
    ],
    templateUrl: './color-contrast.component.html',
    styleUrl: './color-contrast.component.scss'
})
export class ColorContrastComponent implements OnInit {
  stats = '';
  inputColor = model('#f0c030');
  colors: string[] = []
  output: ColorContrastResult = {
    colors: [],
    matrix: [],
  };
  error = '';
  bgColor: string | null = null;

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    this.colors = pickRandomPalette();
    this.inputColor.set(pickRandomColor());
  }

  ngOnInit(): void {
    this.generate();
  }

  addColor() {
    this.colors.push(this.inputColor());
    this.generate();
  }

  removeColor(index: number) {
    this.colors.splice(index, 1);
    this.generate();
  }

  clearColors() {
    this.colors = [];
    this.output = {
      colors: [],
      matrix: [],
    };
  }

  generate() {
    this.error = '';
    this.output = colorContrastChecker(this.colors);

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      colorCount: this.colors.length,
      totalComparisons: this.output.matrix.length
    });
  }
}
