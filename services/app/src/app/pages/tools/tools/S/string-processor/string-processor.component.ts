import { Component, effect, inject, model, OnInit } from '@angular/core';
import { TextInputComponent } from '../../../../../ui/components/text-input/text-input.component';
import { ToolDescription } from '@common/types/tool-description';
import * as toolInfo from '@common/data/tools/S/string-processor.json';
import { StringProcessorAction, stringProcessorActions } from './string-processor-actions';
import { FormsModule } from '@angular/forms';
import { FullPanelToolComponent } from '../../../../../ui/layout/full-panel-tool/full-panel-tool.component';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-string-processor',
    imports: [
        TextInputComponent,
        FormsModule,
        FullPanelToolComponent,
    ],
    templateUrl: './string-processor.component.html',
    styleUrl: './string-processor.component.scss'
})
export class StringProcessorComponent implements OnInit {
  error = '';
  text: string = '';
  undo: string[] = [];
  redo: string[] = [];

  readonly actions = stringProcessorActions;
  filteredActions = stringProcessorActions;
  filter = model<string>('');



  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    effect(() => {
      const f = this.filter().toLowerCase().trim();

      if (!f) {
        this.filteredActions = stringProcessorActions;
        return;
      }


      this.filteredActions = stringProcessorActions.filter(a =>
        a.name.toLowerCase().includes(f)
        || a.tooltip.toLowerCase().includes(f)
        || a.id.toLowerCase().includes(f)
      );
    })
  }

  ngOnInit(): void {
  }

  generate() {
  }

  onUndo() {
    if (this.undo.length === 0) {
      return;
    }

    this.redo.push(this.text);
    this.text = this.undo.pop()!;
  }

  onRedo() {
    if (this.redo.length === 0) {
      return;
    }

    this.undo.push(this.text);
    this.text = this.redo.pop()!;
  }

  execute(action: StringProcessorAction) {
    this.error = '';
    this.undo.push(this.text);
    try {
      this.text = action.action(this.text);
      this.redo = [];

      // Track tool usage
      this.analyticsService.trackToolUse(this.toolInfo.toolId, {
        action: action.id,
        textLength: this.text.length
      });
    } catch (e) {
      this.error = (e as Error).message;
      this.undo.pop();
    }
  }
}
