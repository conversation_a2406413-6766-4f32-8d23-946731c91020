import { Component, effect, inject, OnInit, signal } from '@angular/core';
import { ToolDescription } from '@common/types/tool-description';
import { debounceTime, Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import xmlFormat from 'xml-formatter';
import { FormsModule } from '@angular/forms';
import * as toolInfo from '@common/data/tools/S/svg-to-react.json';
import { TextInputComponent } from '../../../../../ui/components/text-input/text-input.component';
import { TextOutputComponent } from '../../../../../ui/components/text-output/text-output.component';
import { reactConvertAttributesToPropsRecursive } from '../../../../../utils/react';
import { TwoPanelToolComponent } from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import {
  PanelTitleWithErrorComponent
} from '../../../../../ui/components/panel-title-with-error/panel-title-with-error.component';
import { AnalyticsService } from '@/app/services/analytics.service';


@Component({
    selector: 'app-svg-to-react',
    imports: [
        FormsModule,
        TextInputComponent,
        TextOutputComponent,
        TwoPanelToolComponent,
        PanelTitleWithErrorComponent,
    ],
    templateUrl: './svg-to-react.component.html',
    styleUrl: './svg-to-react.component.scss'
})
export class SvgToReactComponent implements OnInit {
  input = signal<string>(`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
  <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
</svg>
`);
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;
  indent = '2';
  component = 'SvgIcon';

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    let output = '';

    try {
      output = this.generateComponent();
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }

    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: this.input().length,
      outputLength: output.length,
      componentName: this.component,
      indent: this.indent
    });
  }

  onForceEncode() {
    this.generate();
  }

  onIndentChange() {
    this.generate();
  }

  onComponentChange() {
    this.generate();
  }

  private getIndent() {
    if (this.indent === '2') {
      return 2;
    } else if (this.indent === '4') {
      return 4;
    } else if (this.indent === 't') {
      return '\t';
    } else {
      return 0;
    }
  }

  generateComponent() {
    const indent = this.getIndentString();

    // Parse SVG.
    const parser = new DOMParser();
    const xml = parser.parseFromString(this.input(), 'text/xml');
    (window as any).svg = xml;
    const root = xml.documentElement;
    if (root.nodeName !== 'svg') {
      throw new Error('Invalid SVG root element, expected <svg>, got <${root.nodeName}>');
    }
    // renameAttributes(root, attributeMaping);
    reactConvertAttributesToPropsRecursive(root);
    root.setAttribute('__xxxprops', '__yyyprops');

    // Serialize XML.
    const serializer = new XMLSerializer();
    const svgInput = serializer.serializeToString(root);

    // Format XML.
    let svg = xmlFormat(svgInput, {
      indentation: indent,
      lineSeparator: '\n',
    }).split('\n').map(line => `${indent}${indent}${line}`).join('\n');
    svg = this.injectProps(svg);

    // Return component code.
    return `import { SVGProps } from 'react';\n\n`
      + `export const ${this.component} = (props: SVGProps<SVGSVGElement>) => {\n`
      + `${indent}return (\n`
      + svg + '\n'
      + `${indent});\n`
      + `};\n`;
  }

  private getIndentString(): string {
    const indent = this.getIndent();
    if (typeof indent === 'number') {
      return ' '.repeat(indent);
    } else {
      return indent;
    }
  }

  private injectProps(svg: string) {
    return svg.replace(/__xxxprops="__yyyprops"/, '{...props}');
  }
}
