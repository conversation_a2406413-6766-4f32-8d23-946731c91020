import { Component, effect, inject, model, OnInit } from '@angular/core';
import { CenterPanelToolComponent } from '@/app/ui/layout/center-panel-tool/center-panel-tool.component';
import { CopyInputComponent } from '@/app/ui/components/copy-input/copy-input.component';
import { InlineErrorBlockComponent } from '@/app/ui/components/inline-error-block/inline-error-block.component';
import { FormsModule } from '@angular/forms';
import { ToolDescription } from '@common/types/tool-description';
import * as toolInfo from '@common/data/tools/R/roman-numeral-converter.json';
import { isPhotoshoot } from '@/environments/utils';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
  selector: 'app-roman-numeral-converter',
  imports: [
    CenterPanelToolComponent,
    CopyInputComponent,
    InlineErrorBlockComponent,
    FormsModule,
  ],
  templateUrl: './roman-numeral-converter.component.html',
  styleUrl: './roman-numeral-converter.component.scss'
})
export class RomanNumeralConverterComponent implements OnInit {
  arabicNumber = model(isPhotoshoot() ? '2025' : '');
  romanNumeral = model('');
  error = '';

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    // Update Roman numeral when Arabic number changes
    effect(() => {
      const arabic = this.arabicNumber();
      if (arabic === '') {
        this.romanNumeral.set('');
        return;
      }

      try {
        const num = parseInt(arabic, 10);
        if (isNaN(num)) {
          this.error = 'Please enter a valid number';
          return;
        }

        if (num === 0) {
          this.romanNumeral.set('nulla');
          this.error = '';
          return;
        }

        if (num < 1 || num > 3999) {
          this.error = 'Number must be between 1 and 3999';
          return;
        }

        this.error = '';
        this.romanNumeral.set(this.convertToRoman(num));

        // Track tool usage for Arabic to Roman conversion
        this.analyticsService.trackToolUse(this.toolInfo.toolId, {
          conversionType: 'arabicToRoman',
          number: num
        });
      } catch (e) {
        this.error = (e as Error).message;
      }
    });

    // Update Arabic number when Roman numeral changes
    effect(() => {
      const roman = this.romanNumeral();
      if (roman === '') {
        this.arabicNumber.set('');
        return;
      }

      if (roman.toLowerCase() === 'nulla') {
        this.arabicNumber.set('0');
        this.error = '';
        return;
      }

      try {
        const num = this.convertToArabic(roman);
        this.error = '';
        this.arabicNumber.set(num.toString());

        // Track tool usage for Roman to Arabic conversion
        this.analyticsService.trackToolUse(this.toolInfo.toolId, {
          conversionType: 'romanToArabic',
          number: num,
          romanLength: roman.length
        });
      } catch (e) {
        this.error = (e as Error).message;
      }
    });
  }

  ngOnInit(): void {
    // Initialize with empty values
  }

  /**
   * Converts an Arabic number to a Roman numeral
   */
  convertToRoman(num: number): string {
    if (num === 0) return 'nulla';
    if (num < 1 || num > 3999) {
      throw new Error('Number must be between 1 and 3999');
    }

    const romanNumerals = [
      { value: 1000, symbol: 'M' },
      { value: 900, symbol: 'CM' },
      { value: 500, symbol: 'D' },
      { value: 400, symbol: 'CD' },
      { value: 100, symbol: 'C' },
      { value: 90, symbol: 'XC' },
      { value: 50, symbol: 'L' },
      { value: 40, symbol: 'XL' },
      { value: 10, symbol: 'X' },
      { value: 9, symbol: 'IX' },
      { value: 5, symbol: 'V' },
      { value: 4, symbol: 'IV' },
      { value: 1, symbol: 'I' }
    ];

    let result = '';
    let remaining = num;

    for (const { value, symbol } of romanNumerals) {
      while (remaining >= value) {
        result += symbol;
        remaining -= value;
      }
    }

    return result;
  }

  /**
   * Converts a Roman numeral to an Arabic number
   */
  convertToArabic(roman: string): number {
    if (roman.toLowerCase() === 'nulla') return 0;

    const romanValues: Record<string, number> = {
      'I': 1,
      'V': 5,
      'X': 10,
      'L': 50,
      'C': 100,
      'D': 500,
      'M': 1000
    };

    let result = 0;
    const upperRoman = roman.toUpperCase();

    // Validate Roman numeral format
    if (!/^[IVXLCDM]+$/.test(upperRoman)) {
      throw new Error('Invalid Roman numeral');
    }

    for (let i = 0; i < upperRoman.length; i++) {
      const current = romanValues[upperRoman[i]];
      const next = i + 1 < upperRoman.length ? romanValues[upperRoman[i + 1]] : 0;

      if (current < next) {
        result += next - current;
        i++; // Skip the next character as we've already processed it
      } else {
        result += current;
      }
    }

    if (result < 1 || result > 3999) {
      throw new Error('Roman numeral must represent a number between 1 and 3999');
    }

    return result;
  }
}
