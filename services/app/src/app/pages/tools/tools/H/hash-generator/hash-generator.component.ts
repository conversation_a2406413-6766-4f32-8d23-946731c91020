import {Component, effect, inject, model, OnInit} from '@angular/core';
import {CenterPanelToolComponent} from '../../../../../ui/layout/center-panel-tool/center-panel-tool.component';
import {CopyInputComponent} from '../../../../../ui/components/copy-input/copy-input.component';
import {InlineErrorBlockComponent} from '../../../../../ui/components/inline-error-block/inline-error-block.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {ToolDescription} from '@common/types/tool-description';
import * as toolInfo from '@common/data/tools/H/hash-generator.json';
import md5 from 'crypto-js/md5';
import sha224 from 'crypto-js/sha224';
import ripemd160 from 'crypto-js/ripemd160';
import sha3 from 'crypto-js/sha3';
import { AnalyticsService } from '@/app/services/analytics.service';


function hexStringToArrayBuffer(hexString: string): ArrayBuffer {
  if (hexString.length % 2 !== 0) {
    throw new Error('Invalid hexString');
  }

  const arrayBuffer = new ArrayBuffer(hexString.length / 2);
  const uint8Array = new Uint8Array(arrayBuffer);

  for (let i = 0; i < hexString.length; i += 2) {
    uint8Array[i / 2] = parseInt(hexString.substr(i, 2), 16);
  }

  return arrayBuffer;
}

const hashFunctions: {
  id: string,
  name: string,
  fn: (input: string) => Promise<ArrayBuffer>,
}[] = [
  {
    id: 'sha1',
    name: 'SHA-1',
    fn: async (input) => {
      const msgUint8 = new TextEncoder().encode(input);
      return await crypto.subtle.digest('SHA-1', msgUint8);
      // const hashArray = Array.from(new Uint8Array(hashBuffer));
      // return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    },
  },
  {
    id: 'sha224',
    name: 'SHA-224',
    fn: async (input) => {
      return Promise.resolve(hexStringToArrayBuffer(sha224(input).toString()));
    },
  },
  {
    id: 'sha256',
    name: 'SHA-256',
    fn: async (input) => {
      const msgUint8 = new TextEncoder().encode(input);
      return await crypto.subtle.digest('SHA-256', msgUint8);
    },
  },
  {
    id: 'sha384',
    name: 'SHA-384',
    fn: async (input) => {
      const msgUint8 = new TextEncoder().encode(input);
      return await crypto.subtle.digest('SHA-384', msgUint8);
    },
  },
  {
    id: 'sha512',
    name: 'SHA-512',
    fn: async (input) => {
      const msgUint8 = new TextEncoder().encode(input);
      return await crypto.subtle.digest('SHA-512', msgUint8);
    },
  },
  {
    id: 'sha3',
    name: 'SHA-3',
    fn: async (input) => {
      return Promise.resolve(hexStringToArrayBuffer(sha3(input).toString()));
    },
  },
  {
    id: 'ripemd160',
    name: 'RIPEMD160',
    fn: async (input) => {
      return Promise.resolve(hexStringToArrayBuffer(ripemd160(input).toString()));
    },
  },
  {
    id: 'md5',
    name: 'MD5',
    fn: async (input) => {
      return Promise.resolve(hexStringToArrayBuffer(md5(input).toString()));
      // string to ArrayBuffer
      // const msgUint8 = new TextEncoder().encode(input);
    },
  },
]

enum HashFormat {
  Hex = 'hex',
  HexBytes = 'hexBytes',
  Base64 = 'base64',
  Base64Url = 'base64url',
  OctalBytes = 'octalBytes',
}

@Component({
    selector: 'app-hash',
    imports: [
        CenterPanelToolComponent,
        CopyInputComponent,
        InlineErrorBlockComponent,
        ReactiveFormsModule,
        FormsModule,
    ],
    templateUrl: './hash-generator.component.html',
    styleUrl: './hash-generator.component.scss'
})
export class HashGeneratorComponent implements OnInit {
  stats = '';
  input = model('Hello World');
  format = model(HashFormat.Hex);
  error = '';
  props = hashFunctions.map(({id, name}) => [id, name]);
  output: Record<string, string> = {};

  hashFormats: [HashFormat, string][] = [
    [HashFormat.Hex, 'Hexadecimnal (base 16)'],
    [HashFormat.HexBytes, 'Hexadecimnal bytes (base 16)'],
    [HashFormat.Base64, 'Base64'],
    [HashFormat.Base64Url, 'Base64 URL'],
    [HashFormat.OctalBytes, 'Octal bytes (base 8)'],
  ]

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    effect(() => {
      this.input();
      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    this.error = '';
    const text = this.input();
    const format = this.format();

    const start = performance.now();
    try {
      this.output = {};
      hashFunctions.forEach(({id, fn}) => {
        fn(text).then((hash) => {
          this.output[id] = this.formatOutput(hash, format);
        }).catch(e => {
          console.log('Hash failed for', id, e);
          this.output[id] = '';
        });
      });
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }

    const end = performance.now();
    this.stats = `Hashing took ${(end - start).toFixed(2)} ms`;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: text.length,
      format: format
    });
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }

  formatOutput(hash: ArrayBuffer, format: HashFormat): string {
    const hashArray = Array.from(new Uint8Array(hash));
    switch (format) {
      case HashFormat.Hex:
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      case HashFormat.HexBytes:
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join(' ');
      case HashFormat.Base64:
        return btoa(String.fromCharCode(...hashArray));
      case HashFormat.Base64Url:
        return btoa(String.fromCharCode(...hashArray)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
      case HashFormat.OctalBytes:
        return hashArray.map(b => b.toString(8).padStart(3, '0')).join(' ');
      default:
        return '';
    }
  }

}

