import { Component, effect, inject, model, OnInit, signal } from '@angular/core';
import { ToolDescription } from '@common/types/tool-description';
import { FormsModule } from '@angular/forms';
import * as toolInfo from '@common/data/tools/W/wifi-qrcode-generator.json';
import { InlineErrorBlockComponent } from '@/app/ui/components/inline-error-block/inline-error-block.component';
import { CopyInputComponent } from '@/app/ui/components/copy-input/copy-input.component';
import { CenterPanelToolComponent } from '@/app/ui/layout/center-panel-tool/center-panel-tool.component';
import qrcode from 'qrcode';
import { ClickRestoreDirective } from '@/app/ui/components/click-restore.directive';
import { WheelNumberDirective } from '@/app/ui/components/wheel-number.directive';
import { isPhotoshoot } from '@/environments/utils';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
  selector: 'app-wifi-qrcode-generator',
  imports: [
    FormsModule,
    InlineErrorBlockComponent,
    CopyInputComponent,
    CenterPanelToolComponent,
    ClickRestoreDirective,
    WheelNumberDirective
  ],
  templateUrl: './wifi-qrcode-generator.component.html',
  styleUrl: './wifi-qrcode-generator.component.scss'
})
export class WifiQrcodeGeneratorComponent implements OnInit {
  // Using value instead of text for binding with app-copy-input
  ssid = model(isPhotoshoot() ? 'PowerDev Tools' : '');
  password = model(isPhotoshoot() ? 'powerdevtools rocks!' : '');
  output = signal<string>('');
  error = '';
  size = model(300);
  minSize = 100;
  maxSize = 1000;
  defaultSize = 300;
  hidden = model(false);

  encryptionType = model<'WPA' | 'WEP' | 'nopass' | 'WPA2-EAP'>('WPA');
  encryptionTypes = [
    { value: 'WPA', label: 'WPA/WPA2/WPA3 (Personal)' },
    { value: 'WPA2-EAP', label: 'WPA2-Enterprise (EAP)' },
    { value: 'WEP', label: 'WEP' },
    { value: 'nopass', label: 'None' }
  ];

  // Enterprise (EAP) specific fields
  eapMethod = model<'PEAP' | 'TLS' | 'TTLS' | 'PWD'>('PEAP');
  eapMethods = [
    { value: 'PEAP', label: 'PEAP' },
    { value: 'TLS', label: 'TLS' },
    { value: 'TTLS', label: 'TTLS' },
    { value: 'PWD', label: 'PWD' }
  ];

  phase2Auth = model<'MSCHAPV2' | 'GTC' | 'NONE'>('MSCHAPV2');
  phase2Auths = [
    { value: 'MSCHAPV2', label: 'MSCHAPv2' },
    { value: 'GTC', label: 'GTC' },
    { value: 'NONE', label: 'None' }
  ];

  identity = model('');

  errorCorrectionLevel = model<'L' | 'M' | 'Q' | 'H'>('M');
  errorCorrectionLevels = [
    { value: 'L', label: 'Low (7%)' },
    { value: 'M', label: 'Medium (15%)' },
    { value: 'Q', label: 'Quartile (25%)' },
    { value: 'H', label: 'High (30%)' }
  ];

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  constructor() {
    effect(() => {
      this.ssid();
      this.password();
      this.encryptionType();
      this.size();
      this.errorCorrectionLevel();
      this.hidden();
      this.eapMethod();
      this.phase2Auth();
      this.identity();
      this.generate();
    });

  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    if (!this.ssid()) {
      this.error = 'Please enter a WiFi SSID (network name)';
      return;
    }

    if (this.encryptionType() !== 'nopass' && !this.password()) {
      this.error = 'Please enter a password for the encrypted network';
      return;
    }

    if (this.encryptionType() === 'WPA2-EAP' && !this.identity()) {
      this.error = 'Please enter an identity for the WPA2-Enterprise network';
      return;
    }

    try {
      let wifiString;

      if (this.encryptionType() === 'WPA2-EAP') {
        // Format for WPA2-Enterprise
        // WIFI:T:WPA2-EAP;S:<ssid>;P:<password>;H:<hidden>;E:<eap method>;PH2:<phase 2 method>;I:<identity>;;
        wifiString = `WIFI:T:${this.encryptionType()};S:${this.escapeSpecialChars(this.ssid())};P:${this.escapeSpecialChars(this.password())};H:${this.hidden()};E:${this.eapMethod()};PH2:${this.phase2Auth()};I:${this.escapeSpecialChars(this.identity())};;`;
      } else {
        // Format for standard WiFi: WIFI:T:<encryption type>;S:<ssid>;P:<password>;H:true/false;;
        wifiString = `WIFI:T:${this.encryptionType()};S:${this.escapeSpecialChars(this.ssid())};P:${this.escapeSpecialChars(this.password())};H:${this.hidden()};;`;
      }

      qrcode.toDataURL(wifiString, {
        errorCorrectionLevel: this.errorCorrectionLevel(),
        width: this.size(),
        margin: 1
      }, (err, url) => {
        if (err) {
          this.error = 'Failed to generate QR code: ' + err.message;
          return;
        }
        this.error = '';
        this.output.set(url);

        // Track tool usage
        this.analyticsService.trackToolUse(this.toolInfo.toolId, {
          encryptionType: this.encryptionType(),
          size: this.size(),
          errorCorrectionLevel: this.errorCorrectionLevel(),
          hasPassword: !!this.password(),
          isHidden: this.hidden(),
          isEnterprise: this.encryptionType() === 'WPA2-EAP'
        });
      });
    } catch (e) {
      this.error = 'Failed to generate QR code: ' + (e as Error).message;
    }
  }

  // Escape special characters according to the WiFi QR code format
  private escapeSpecialChars(text: string): string {
    return text
      .replace(/\\/g, '\\\\')
      .replace(/"/g, '\\"')
      .replace(/;/g, '\\;')
      .replace(/,/g, '\\,')
      .replace(/:/g, '\\:');
  }

  onGenerate(event: Event) {
    event.preventDefault();
    this.generate();
  }

  downloadQRCode() {
    const link = document.createElement('a');
    link.download = 'wifi-qrcode.png';
    link.href = this.output();
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
