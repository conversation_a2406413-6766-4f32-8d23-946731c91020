import {Component, effect, inject, OnInit, signal} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {debounceTime, Subject} from 'rxjs';
import {takeUntilDestroyed} from '@angular/core/rxjs-interop';
import {
  PanelTitleWithErrorComponent
} from '../../../../../ui/components/panel-title-with-error/panel-title-with-error.component';
import {TextInputComponent} from '../../../../../ui/components/text-input/text-input.component';
import {TextOutputComponent} from '../../../../../ui/components/text-output/text-output.component';
import {TwoPanelToolComponent} from '../../../../../ui/layout/two-panel-tool/two-panel-tool.component';
import * as toolInfo from '@common/data/tools/D/dotenv-to-json.json';
import {ToolDescription} from '@common/types/tool-description';
import {dotEnvParse} from '../../../../../utils/dotenv-parse';
import { AnalyticsService } from '@/app/services/analytics.service';

@Component({
    selector: 'app-dotenv-to-json',
    imports: [
        FormsModule,
        PanelTitleWithErrorComponent,
        TextInputComponent,
        TextOutputComponent,
        TwoPanelToolComponent
    ],
    templateUrl: './dotenv-to-json.component.html',
    styleUrl: './dotenv-to-json.component.scss'
})
export class DotenvToJsonComponent implements OnInit {
  input = signal<string>('HOST=localhost\nPORT=3000\nSECRET="I will not tell you what it is"');
  output = signal<string>('');
  stats = signal('');
  inputSize = signal('');
  error = '';
  inputDirty = false;
  indent = '2';

  protected readonly toolInfo: ToolDescription = toolInfo;

  private analyticsService = inject(AnalyticsService);

  private subject = new Subject<void>();
  private refresh = this.subject.pipe(debounceTime(800));

  constructor() {
    effect(() => {
      this.input();
      this.inputDirty = true;
      this.subject.next();
    });

    this.refresh.pipe(takeUntilDestroyed()).subscribe(() => {
      if (!this.inputDirty) {
        return;
      }

      this.generate();
    });
  }

  ngOnInit(): void {
    this.generate();
  }

  generate() {
    let output = '';
    const indent = this.getIndent();

    try {
      const doc = dotEnvParse(this.input());
      output = JSON.stringify(doc, null, indent);
    } catch (e) {
      this.error = (e as Error).message;
      return;
    }

    this.error = '';
    this.output.set(output);
    this.stats.set(`Size: ${output.length}`);
    this.inputSize.set(`Size: ${this.input().length}`);
    this.inputDirty = false;

    // Track tool usage
    this.analyticsService.trackToolUse(this.toolInfo.toolId, {
      inputLength: this.input().length,
      outputLength: output.length,
      indent: this.indent
    });
  }

  onForceEncode() {
    this.generate();
  }

  onIndentChange() {
    this.generate();
  }

  private getIndent() {
    if (this.indent === '2') {
      return 2;
    } else if (this.indent === '4') {
      return 4;
    } else if (this.indent === 't') {
      return '\t';
    } else {
      return 0;
    }
  }
}
