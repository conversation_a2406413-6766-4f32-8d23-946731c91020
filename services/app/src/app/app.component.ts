import { Component, inject, OnInit, ViewEncapsulation } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SidebarMenuComponent } from './ui/layout/sidebar-menu/sidebar-menu.component';
import { NavbarMenuComponent } from './ui/layout/navbar-menu/navbar-menu.component';
import { environment } from '../environments/environment';
import { AuthService } from './services/auth.service';
import { NoAccessCardComponent } from './ui/components/no-access-card/no-access-card.component';
import { LogoComponent } from './ui/logo/logo.component';
import { PwaNewTabDirective } from './ui/directives/pwa-new-tab.directive';
import { AnalyticsComponent } from '@/app/ui/utils/analytics/analytics.component';

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    RouterModule,
    SidebarMenuComponent,
    NavbarMenuComponent,
    NoAccessCardComponent,
    LogoComponent,
    PwaNewTabDirective,
    AnalyticsComponent,
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class AppComponent implements OnInit {
  landingPageUrl = environment.landingPageUrl;

  auth = inject(AuthService);

  authStatus = this.auth.getStatusSignal();

  ngOnInit() {

  }

}
