const fs = require('fs');
const path = require('path');

// List of all tool component files that need to be updated
const toolFiles = [
  'services/app/src/app/pages/tools/tools/A/angular-pwa-icon-generator/angular-pwa-icon-generator.component.ts',
  'services/app/src/app/pages/tools/tools/C/color-contrast/color-contrast.component.ts',
  'services/app/src/app/pages/tools/tools/C/color-contrast-validator/color-contrast-validator.component.ts',
  'services/app/src/app/pages/tools/tools/C/color-palette-generator/color-palette-generator.component.ts',
  'services/app/src/app/pages/tools/tools/C/color-swatch-generator/color-swatch-generator.component.ts',
  'services/app/src/app/pages/tools/tools/D/dotenv-to-json/dotenv-to-json.component.ts',
  'services/app/src/app/pages/tools/tools/F/favicon-generator/favicon-generator.component.ts',
  'services/app/src/app/pages/tools/tools/H/html-entities/html-entities.component.ts',
  'services/app/src/app/pages/tools/tools/I/image-to-data-url/image-to-data-url.component.ts',
  'services/app/src/app/pages/tools/tools/J/json-sanitizer/json-sanitizer.component.ts',
  'services/app/src/app/pages/tools/tools/J/json-to-dotenv/json-to-dotenv.component.ts',
  'services/app/src/app/pages/tools/tools/J/json-to-toml/json-to-toml.component.ts',
  'services/app/src/app/pages/tools/tools/J/json-to-yaml/json-to-yaml.component.ts',
  'services/app/src/app/pages/tools/tools/J/jwt-decoder/jwt-decoder.component.ts',
  'services/app/src/app/pages/tools/tools/J/jwt-secret-generator/jwt-secret-generator.component.ts',
  'services/app/src/app/pages/tools/tools/L/latex-to-unicode/latex-to-unicode.component.ts',
  'services/app/src/app/pages/tools/tools/L/lorem-ipsum-generator/lorem-ipsum-generator.component.ts',
  'services/app/src/app/pages/tools/tools/N/numeronym/numeronym.component.ts',
  'services/app/src/app/pages/tools/tools/P/pdf-generator/pdf-generator.component.ts',
  'services/app/src/app/pages/tools/tools/P/pdf-merger/pdf-merger.component.ts',
  'services/app/src/app/pages/tools/tools/P/pdf-to-csv/pdf-to-csv.component.ts',
  'services/app/src/app/pages/tools/tools/P/pdf-to-text/pdf-to-text.component.ts',
  'services/app/src/app/pages/tools/tools/Q/qrcode-generator/qrcode-generator.component.ts',
  'services/app/src/app/pages/tools/tools/Q/quoted-printable/quoted-printable.component.ts',
  'services/app/src/app/pages/tools/tools/R/roman-numeral-converter/roman-numeral-converter.component.ts',
  'services/app/src/app/pages/tools/tools/S/string-processor/string-processor.component.ts',
  'services/app/src/app/pages/tools/tools/S/svg-to-react/svg-to-react.component.ts',
  'services/app/src/app/pages/tools/tools/S/svg-to-react-native/svg-to-react-native.component.ts',
  'services/app/src/app/pages/tools/tools/T/text-obfuscator/text-obfuscator.component.ts',
  'services/app/src/app/pages/tools/tools/T/text-stats/text-stats.component.ts',
  'services/app/src/app/pages/tools/tools/T/text-to-nato-alphabet/text-to-nato-alphabet.component.ts',
  'services/app/src/app/pages/tools/tools/T/toml-formatter/toml-formatter.component.ts',
  'services/app/src/app/pages/tools/tools/T/toml-to-json/toml-to-json.component.ts',
  'services/app/src/app/pages/tools/tools/U/uri-encoder/uri-encoder.component.ts',
  'services/app/src/app/pages/tools/tools/U/url-parser/url-parser.component.ts',
  'services/app/src/app/pages/tools/tools/W/wifi-qrcode-generator/wifi-qrcode-generator.component.ts',
  'services/app/src/app/pages/tools/tools/Y/yaml-formatter/yaml-formatter.component.ts',
  'services/app/src/app/pages/tools/tools/Y/yaml-to-json/yaml-to-json.component.ts'
];

function updateToolFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 1. Add inject to imports if not present
    if (!content.includes('inject') && content.includes('import {Component')) {
      content = content.replace(
        /import \{Component,([^}]+)\} from '@angular\/core';/,
        (match, imports) => {
          if (!imports.includes('inject')) {
            return `import {Component, inject,${imports}} from '@angular/core';`;
          }
          return match;
        }
      );
      modified = true;
    }

    // 2. Add AnalyticsService import if not present
    if (!content.includes('AnalyticsService')) {
      const lastImportIndex = content.lastIndexOf('import ');
      const nextLineIndex = content.indexOf('\n', lastImportIndex);
      content = content.slice(0, nextLineIndex) + 
                '\nimport { AnalyticsService } from \'@/app/services/analytics.service\';' +
                content.slice(nextLineIndex);
      modified = true;
    }

    // 3. Add private analyticsService injection if not present
    if (!content.includes('analyticsService = inject(AnalyticsService)')) {
      const toolInfoMatch = content.match(/protected readonly toolInfo: ToolDescription = toolInfo;/);
      if (toolInfoMatch) {
        const insertIndex = toolInfoMatch.index + toolInfoMatch[0].length;
        content = content.slice(0, insertIndex) +
                  '\n  \n  private analyticsService = inject(AnalyticsService);' +
                  content.slice(insertIndex);
        modified = true;
      }
    }

    // 4. Add analytics tracking to generate method if not present
    if (!content.includes('this.analyticsService.trackToolUse')) {
      // Find the generate method and add tracking before the closing brace
      const generateMethodMatch = content.match(/generate\(\)[^{]*\{([\s\S]*?)\n  \}/);
      if (generateMethodMatch) {
        const methodContent = generateMethodMatch[1];
        // Look for common patterns that indicate successful completion
        if (methodContent.includes('this.output.set') || 
            methodContent.includes('this.stats.set') ||
            methodContent.includes('this.error = \'\'')) {
          
          const insertPoint = generateMethodMatch.index + generateMethodMatch[0].length - 4; // Before closing }
          content = content.slice(0, insertPoint) +
                    '\n    \n    // Track tool usage\n    this.analyticsService.trackToolUse(this.toolInfo.toolId, {});\n  ' +
                    content.slice(insertPoint + 2);
          modified = true;
        }
      }
    }

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Updated: ${filePath}`);
    } else {
      console.log(`No changes needed: ${filePath}`);
    }

  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

// Process all tool files
toolFiles.forEach(updateToolFile);

console.log('Analytics integration complete!');
